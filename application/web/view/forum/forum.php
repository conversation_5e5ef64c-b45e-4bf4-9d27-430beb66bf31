<div id="app">
    <!-- pageBanner -->
    <div class="pageBanner" style="background: url(<?=$dir_public?>/web/images/pageBg.jpg) center center no-repeat;">
        <div class="box">
            <div class="wrap between">
                <div class="location left">
                    <div class="iconfont icon-shouye sel"></div>
                    <p>当前位置：</p>
                    <a href="/">首页</a>
                    <span class="iconfont icon-jiantou"></span>
                    <a href="javascript:;" class="on">论坛日程</a>
                </div>
                <!-- 添加语言切换和登录链接 -->
                <div class="right headerActions">
                    <a href="/en/index/index.html" class="langSwitch">EN</a>
                    <a href="https://s.pujiangforum.cn/ekajvCOr" class="loginLink">注册/登录</a>
                </div>
            </div>
        </div>
    </div>
    
    <!-- pageTab -->
    <div class="pageTab1">
        <div class="wrap centerT">
            <ul class="left">
                <li :class="sele === 1 ? 'on' : ''" @click="changeTime('2024-09-07 00:00:00','2024-09-07 23:59:59', 1)">
                    <a href="javascript:;">9月7日</a>
                </li>
                <li :class="sele === 2 ? 'on' : ''" @click="changeTime('2024-09-08 00:00:00','2024-09-08 23:59:59', 2)">
                    <a href="javascript:;">9月8日</a>
                </li>
                <li :class="sele === 3 ? 'on' : ''" @click="changeTime('2024-09-09 00:00:00','2024-09-09 23:59:59', 3)">
                    <a href="javascript:;">9月9日</a>
                </li>
            </ul>
            <form class="between" action="" autocomplete="off">
                <input type="text" name="" value="" placeholder="搜索嘉宾姓名/议程" />
                <button class="iconfont icon-sousuo" type="button"></button>
            </form>
        </div>
    </div>


    <!-- pageForum -->
    <div class="pageForum scheduleBox">
        <div class="wrap">
            <!-- 错误状态 -->
            <div v-if="error" class="error">
                <p>敬请期待</p>
            </div>

            <!-- 论坛列表 -->
            <div v-for="(item,index) in lists" :key="index" @click="select(index)" v-if="!error">
                <div class="liBox" v-if="item.lt.FC1I3KSV08QWDZVDU">
                    <div class="Box between" :class="sel === index ? 'on' : ''">
                        <div class="titBox">
                           <div class="left">
                                <div class="time left">
                                    <div class="iconfont icon-shijian"></div>
                                    <p>{{item.lt.start_time}} - {{item.lt.end_time}}</p>
                                </div>
                                <div class="address left">
                                    <div class="iconfont icon-weizhi1"></div>
                                    <p>会议室：{{item.lt.FC1I3KSV03VWDZVUO_name}}</p>
                                    </div>
                                </div>
                           </div>

                           <div class="left">
                                <div class="tit">
                                    青年创新讲坛（Y HUBS）融自然于城市
                                </div>
                                <div class="label label2 centerT">
                                    <div class="iconfont icon-zhibo"></div>
                                    <p>直播中</p>
                                </div>
                           </div>
                        </div>
                        <div class="btnBox right">
                            <div class="iconfont icon-jiantou"></div>
                        </div>
                    </div>
                    <div class="contBox" :class="sel === index ? 'sel' : ''">
                        <div class="cont">
                            <div class="t">论坛地点：中国•上海</div>
                            <div class="t left">
                                <p>主办单位：中华人民共和国科学技术部、上海市人民政府</p>
                                <p>承办单位：科技部政策法规与创新体系建设司、同济大学</p>
                            </div>
                            <div class="t1">主题诠释</div>
                            <div class="t2">党的二十大报告在部署完善科技创新体系任务时强调，“统筹推进国际科技创新中心、区域科技创新中心建设”。<br>目前北京、上海、粤港澳大湾区三个国际创新中心建设正在快速发展中，各地区根据自身优势和特点，加大科技创新投入和产业升级力度，加强与全球各地的科技合作与交流，不断提升自身在<br>目前北京、上海、粤港澳大湾区三个国际创新中心建设正在快速发展中，各地区根据自身优势和特点，加大科技创新投入和产业升级力度，加强与全球各地的科技合作与交流，不断提升自身在<br>目前北京、上海、粤港澳大湾区三个国际创新中心建设正在快速发展中，各地区根据自身优势和特点，加大科技创新投入和产业升级力度，加强与全球各地的科技合作与交流，不断提升自身在</div>
                        </div>
                        <div class="dl" v-if="item.yc_row != ''">
                            <div class="dt between" v-for="(innerItem,innerIndex) in item.yc_row" :key="innerIndex">
                                <div class="time">{{innerItem.duration_sd}}</div>
                                <div class="dd">
                                    <div class="t3">
                                        <span v-if="innerItem.topicType">{{innerItem.topicType}}</span>
                                        {{innerItem.topicCn}}
                                    </div>
                                    <div class="p1"><span>演讲嘉宾</span>刘冬梅，中国科学技术发展战略研究院党委书记</div>
                                </div>
                            </div>
                        </div>

                        <!-- 嘉宾墙 -->
                        <div class="guestBox" v-if="item.guests && item.guests.length > 0">
                            <div class="guestTit">嘉宾墙</div>
                            <ul class="guestList left"  v-for="(guest, gIndex) in item.guests" :key="gIndex">
                                <li>
                                    <div class="img"><img src="/web/images/guest01.jpg" alt=""></div>
                                    <div class="name">全黎黎</div>
                                    <div class="p">无花果有限公司总经理、创始人合伙人</div>
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
    new Vue({
        el: '#app',
        data: {
            start_time: '2024-09-07 00:00:00',
            end_time: '2024-09-07 23:59:59',
            lists: [],
            sel: null,
            sele: 1,
            expandedItems: {}, // 用于跟踪展开状态
            loading: false, // 加载状态
            error: null, // 错误信息
        },
        created() {
            this.forumList(this.start_time, this.end_time)

            let nowTime = new Date().getTime()
            let time = new Date('2024-09-07 23:59:59').getTime()
            if(nowTime > time) {
                this.start_time = '2024-09-08 00:00:00'
                this.end_time = '2024-09-08 23:59:59'
                this.sele = 2
                this.forumList(this.start_time, this.end_time)
            }

            let time1 = new Date('2024-09-08 23:59:59').getTime()
            if(nowTime > time1) {
                this.start_time = '2024-09-09 00:00:00'
                this.end_time = '2024-09-09 23:59:59'
                this.sele = 3
                this.forumList(this.start_time, this.end_time)
            }
        },
        methods: {
            changeTime(start, end, index) {
                this.forumList(start, end)
                this.sele = index
                this.sel = null
                this.expandedItems = {} // 重置展开状态
            },

            forumList(start, end){
                var that = this
                this.loading = true
                this.error = null

                axios({
                    method: 'post',
                    url: '<?=url('api/schedule/schedule_zs')?>',
                    data: {
                        start_time: start,
                        end_time: end,
                    }
                }).then((res) => {
                    this.loading = false
                    if (res.data.code == 200) {
                        let lists = res.data.data
                        lists.forEach((item, index) => {
                            let now = new Date().getTime()
                            let startTime = new Date(item.lt.forumtime[0]).getTime()
                            let endTime = new Date(item.lt.forumtime[1]).getTime()
                            item.lt.start_time = item.lt.forumtime[0].slice(5, 16)
                            item.lt.end_time = item.lt.forumtime[1].slice(10, 16)

                            // 设置论坛状态
                            if(now < startTime) {
                                item.lt.forumtime_type = 1  // 未开始
                            } else if(now > startTime && now < endTime) {
                                item.lt.forumtime_type = 2  // 进行中
                            } else if(now > endTime) {
                                item.lt.forumtime_type = 3  // 已结束
                            }

                            // 处理嘉宾信息（示例数据）
                            item.guests = [
                                {name: '全黎黎', title: '无花果有限公司总经理、创始人合伙人', avatar: '/web/images/guest01.jpg'}
                            ]
                        })
                        this.lists = lists
                    } else {
                        this.error = '获取数据失败，请稍后重试'
                    }
                }).catch((error) => {
                    this.loading = false
                    this.error = '网络错误，请检查网络连接'
                    console.error('API Error:', error)
                })
            },

            select(index) {
                if (this.sel === index) {
                    this.sel = null; // 再次点击当前项时收起
                } else {
                    this.sel = index; // 点击其他项时当前项展开，其他项收起
                }
            },

            toggleExpand(index) {
                this.$set(this.expandedItems, index, !this.expandedItems[index])
            },

            handleImageError(event) {
                event.target.src = '/web/images/guest01.jpg'
            }
        }
    })
</script>

<style>
/* 头部操作区域样式 */
.headerActions {
    display: flex;
    align-items: center;
    gap: 20px;
}

.langSwitch, .loginLink {
    color: #fff;
    text-decoration: none;
    padding: 8px 16px;
    border-radius: 4px;
    transition: background-color 0.3s;
}

.langSwitch:hover, .loginLink:hover {
    background-color: rgba(255, 255, 255, 0.1);
}

/* 直播状态样式 */
.liveStatus {
    display: inline-block;
    background: #ff4444;
    color: white;
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 12px;
    margin-right: 8px;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.7; }
    100% { opacity: 1; }
}

/* 展开按钮样式 */
.show .icon-jiantou.expanded {
    transform: rotate(180deg);
}

.show .icon-jiantou {
    transition: transform 0.3s ease;
}

/* 嘉宾墙样式 */
.guestWall {
    margin-top: 30px;
    padding: 20px;
    background: #f8f9fa;
    border-radius: 8px;
}

.guestWall h3 {
    margin-bottom: 20px;
    color: #333;
    font-size: 18px;
}

.guestList {
    display: flex;
    flex-wrap: wrap;
    gap: 20px;
}

.guestItem {
    display: flex;
    align-items: center;
    background: white;
    padding: 15px;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    min-width: 280px;
}

.guestImg {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    overflow: hidden;
    margin-right: 15px;
    flex-shrink: 0;
}

.guestImg img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.guestInfo {
    flex: 1;
}

.guestName {
    font-weight: bold;
    color: #333;
    margin-bottom: 5px;
}

.guestTitle {
    color: #666;
    font-size: 14px;
    line-height: 1.4;
}

/* 联系信息样式 */
.contactSection {
    background: #f5f5f5;
    padding: 40px 0;
    margin-top: 40px;
}

.contactBox {
    display: flex;
    justify-content: space-between;
    gap: 40px;
}

.contactItem {
    flex: 1;
}

.contactItem h4 {
    color: #333;
    margin-bottom: 15px;
    font-size: 18px;
    border-bottom: 2px solid #007bff;
    padding-bottom: 8px;
}

.contactItem p {
    margin-bottom: 8px;
    color: #666;
    line-height: 1.6;
}

.qrCodes {
    display: flex;
    gap: 20px;
    margin-top: 15px;
}

.qrItem {
    text-align: center;
}

.qrItem img {
    width: 80px;
    height: 80px;
    border-radius: 8px;
    margin-bottom: 8px;
}

.qrItem p {
    font-size: 12px;
    color: #666;
}

/* 加载和错误状态样式 */
.loading {
    text-align: center;
    padding: 40px;
    color: #666;
}

.error {
    text-align: center;
    padding: 40px;
    color: #ff4444;
    background: #fff5f5;
    border-radius: 8px;
    margin: 20px 0;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .contactBox {
        flex-direction: column;
        gap: 30px;
    }

    .guestList {
        flex-direction: column;
    }

    .guestItem {
        min-width: auto;
    }

    .qrCodes {
        justify-content: center;
    }

    .headerActions {
        flex-direction: column;
        gap: 10px;
    }
}
</style>
