<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>论坛日程测试页面</title>
    <script src="https://cdn.jsdelivr.net/npm/vue@2.6.14/dist/vue.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/axios/dist/axios.min.js"></script>
    <style>
        body { font-family: Arial, sans-serif; margin: 0; padding: 20px; }
        .wrap { max-width: 1200px; margin: 0 auto; }
        .between { display: flex; justify-content: space-between; align-items: center; }
        .left { display: flex; align-items: center; }
        .right { display: flex; align-items: center; }
        .pageBanner { background: #007bff; color: white; padding: 20px 0; }
        .pageTab ul { display: flex; list-style: none; padding: 0; margin: 20px 0; }
        .pageTab li { margin-right: 20px; padding: 10px 20px; background: #f8f9fa; cursor: pointer; }
        .pageTab li.on { background: #007bff; color: white; }
        .liBox { border: 1px solid #ddd; margin: 20px 0; padding: 20px; }
        .Box.on { background: #f8f9fa; }
        .contBox { display: none; padding: 20px; }
        .contBox.sel { display: block; }
        .btn { padding: 10px 20px; background: #007bff; color: white; text-decoration: none; border-radius: 4px; }
        .btn:hover { background: #0056b3; }
        .loading, .error { text-align: center; padding: 40px; }
        .error { color: #dc3545; background: #f8d7da; border-radius: 4px; }
        .headerActions { gap: 20px; }
        .langSwitch, .loginLink { color: white; text-decoration: none; padding: 8px 16px; }
        .liveStatus { background: #dc3545; color: white; padding: 2px 8px; border-radius: 12px; font-size: 12px; }
        .guestWall { margin-top: 20px; padding: 20px; background: #f8f9fa; }
        .guestList { display: flex; flex-wrap: wrap; gap: 20px; }
        .guestItem { display: flex; align-items: center; background: white; padding: 15px; border-radius: 8px; }
        .guestImg { width: 60px; height: 60px; border-radius: 50%; overflow: hidden; margin-right: 15px; }
        .guestImg img { width: 100%; height: 100%; object-fit: cover; }
        .contactSection { background: #f5f5f5; padding: 40px 0; margin-top: 40px; }
        .contactBox { display: flex; gap: 40px; }
        .contactItem { flex: 1; }
        .contactItem h4 { color: #333; margin-bottom: 15px; border-bottom: 2px solid #007bff; padding-bottom: 8px; }
        .qrCodes { display: flex; gap: 20px; margin-top: 15px; }
        .qrItem { text-align: center; }
        .qrItem img { width: 80px; height: 80px; border-radius: 8px; margin-bottom: 8px; }
    </style>
</head>
<body>
    <div id="app">
        <!-- pageBanner -->
        <div class="pageBanner">
            <div class="wrap between">
                <div class="location left">
                    <p>当前位置：<a href="/" style="color: white;">首页</a> > 论坛日程</p>
                </div>
                <div class="right headerActions">
                    <a href="#" class="langSwitch">EN</a>
                    <a href="#" class="loginLink">注册/登录</a>
                </div>
            </div>
        </div>
        
        <!-- pageTab -->
        <div class="pageTab">
            <div class="wrap">
                <ul>
                    <li :class="sele === 1 ? 'on' : ''" @click="changeTime('2024-09-07', 1)">
                        <a href="javascript:;">9月7日</a>
                    </li>
                    <li :class="sele === 2 ? 'on' : ''" @click="changeTime('2024-09-08', 2)">
                        <a href="javascript:;">9月8日</a>
                    </li>
                    <li :class="sele === 3 ? 'on' : ''" @click="changeTime('2024-09-09', 3)">
                        <a href="javascript:;">9月9日</a>
                    </li>
                </ul>
            </div>
        </div>

        <!-- pageForum -->
        <div class="pageForum scheduleBox">
            <div class="wrap">
                <!-- 加载状态 -->
                <div v-if="loading" class="loading">
                    <p>正在加载论坛日程...</p>
                </div>
                
                <!-- 错误状态 -->
                <div v-if="error" class="error">
                    <p>{{error}}</p>
                    <button @click="loadTestData()" class="btn">重新加载</button>
                </div>
                
                <!-- 论坛列表 -->
                <div v-for="(item,index) in lists" :key="index" @click="select(index)" v-if="!loading && !error">
                    <div class="liBox">
                        <div class="Box" :class="sel === index ? 'on' : ''">
                            <div class="titBox">
                                <div class="left">
                                    <div class="tit">{{item.title}}</div>
                                </div>
                                <div class="address left">
                                    <p>会议室：{{item.room}}</p>
                                    <p>会议时间：{{item.time}}</p>
                                </div>
                            </div>
                            <div class="btnBox right">
                                <a href="#" class="btn" v-if="item.status === 'live'">
                                    <span class="liveStatus">直播中</span>
                                    去看直播
                                </a>
                                <span v-else>{{item.statusText}}</span>
                            </div>
                        </div>
                        <div class="contBox" :class="sel === index ? 'sel' : ''">
                            <div class="cont">
                                <div class="t">论坛地点：中国•上海</div>
                                <div class="t">主办单位：中华人民共和国科学技术部、上海市人民政府</div>
                                <div class="t1">主题诠释</div>
                                <div class="t2">{{item.description}}</div>
                            </div>
                            
                            <!-- 嘉宾墙 -->
                            <div class="guestWall" v-if="item.guests && item.guests.length > 0">
                                <h3>嘉宾墙</h3>
                                <div class="guestList">
                                    <div class="guestItem" v-for="(guest, gIndex) in item.guests" :key="gIndex">
                                        <div class="guestImg">
                                            <img :src="guest.avatar" :alt="guest.name">
                                        </div>
                                        <div class="guestInfo">
                                            <div class="guestName">{{guest.name}}</div>
                                            <div class="guestTitle">{{guest.title}}</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 联系信息 -->
        <div class="contactSection">
            <div class="wrap">
                <div class="contactBox">
                    <div class="contactItem">
                        <h4>学术联络</h4>
                        <p>联系人：吴婷</p>
                        <p>电话：86-21-65985664</p>
                        <p>传真：86-21-54065150</p>
                        <p>邮箱：<EMAIL></p>
                        <p>地址：上海市徐汇区淮海中路1634号3号楼102室</p>
                    </div>
                    <div class="contactItem">
                        <h4>参会联络</h4>
                        <p>联系人：沈思思</p>
                        <p>电话：86-21-54065125</p>
                        <p>传真：86-21-54065150</p>
                        <p>邮箱：<EMAIL></p>
                        <p>地址：上海市徐汇区钦州路100号2号楼308室</p>
                    </div>
                    <div class="contactItem">
                        <h4>扫一扫加关注</h4>
                        <div class="qrCodes">
                            <div class="qrItem">
                                <img src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iODAiIGhlaWdodD0iODAiIHZpZXdCb3g9IjAgMCA4MCA4MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjgwIiBoZWlnaHQ9IjgwIiBmaWxsPSIjRjVGNUY1Ii8+Cjx0ZXh0IHg9IjQwIiB5PSI0NSIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZm9udC1zaXplPSIxMiIgZmlsbD0iIzk5OTk5OSI+5b6u5L+h5pyN5YqhPC90ZXh0Pgo8L3N2Zz4K" alt="官方微信服务号">
                                <p>官方微信服务号</p>
                            </div>
                            <div class="qrItem">
                                <img src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iODAiIGhlaWdodD0iODAiIHZpZXdCb3g9IjAgMCA4MCA4MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjgwIiBoZWlnaHQ9IjgwIiBmaWxsPSIjRjVGNUY1Ii8+Cjx0ZXh0IHg9IjQwIiB5PSI0NSIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZm9udC1zaXplPSIxMiIgZmlsbD0iIzk5OTk5OSI+6K6i6ZiF5Y+3PC90ZXh0Pgo8L3N2Zz4K" alt="官方微信订阅号">
                                <p>官方微信订阅号</p>
                            </div>
                            <div class="qrItem">
                                <img src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iODAiIGhlaWdodD0iODAiIHZpZXdCb3g9IjAgMCA4MCA4MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjgwIiBoZWlnaHQ9IjgwIiBmaWxsPSIjRjVGNUY1Ii8+Cjx0ZXh0IHg9IjQwIiB5PSI0NSIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZm9udC1zaXplPSIxMiIgZmlsbD0iIzk5OTk5OSI+5a6Y5pa55b6u5Y2aPC90ZXh0Pgo8L3N2Zz4K" alt="官方微博">
                                <p>官方微博</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        new Vue({
            el: '#app',
            data: {
                lists: [],
                sel: null, 
                sele: 1,
                loading: false,
                error: null,
            },
            created() {
                this.loadTestData()
            },
            methods: {
                changeTime(date, index) {
                    this.sele = index
                    this.sel = null
                    this.loadTestData()
                },

                loadTestData() {
                    this.loading = true
                    this.error = null
                    
                    // 模拟API调用
                    setTimeout(() => {
                        this.loading = false
                        this.lists = [
                            {
                                title: '青年创新讲坛（Y HUBS）融自然于城市',
                                room: '张江科学会堂 张江厅',
                                time: '09:30 - 12:30',
                                status: 'live',
                                statusText: '直播中',
                                description: '党的二十大报告在部署完善科技创新体系任务时强调，"统筹推进国际科技创新中心、区域科技创新中心建设"。目前北京、上海、粤港澳大湾区三个国际创新中心建设正在快速发展中...',
                                guests: [
                                    {name: '全黎黎', title: '无花果有限公司总经理、创始人合伙人', avatar: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjAiIGhlaWdodD0iNjAiIHZpZXdCb3g9IjAgMCA2MCA2MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGNpcmNsZSBjeD0iMzAiIGN5PSIzMCIgcj0iMzAiIGZpbGw9IiNGNUY1RjUiLz4KPHN2ZyB4PSIyMCIgeT0iMjAiIHdpZHRoPSIyMCIgaGVpZ2h0PSIyMCIgdmlld0JveD0iMCAwIDI0IDI0IiBmaWxsPSJub25lIj4KPHN2ZyB3aWR0aD0iMjQiIGhlaWdodD0iMjQiIHZpZXdCb3g9IjAgMCAyNCAyNCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTEyIDEyQzE0LjIwOTEgMTIgMTYgMTAuMjA5MSAxNiA4QzE2IDUuNzkwODYgMTQuMjA5MSA0IDEyIDRDOS43OTA4NiA0IDggNS43OTA4NiA4IDhDOCAxMC4yMDkxIDkuNzkwODYgMTIgMTIgMTJaIiBmaWxsPSIjOTk5OTk5Ii8+CjxwYXRoIGQ9Ik0xMiAxNEM5LjMzIDEzLjk5IDcgMTYuMzYgNyAxOVYyMEgxN1YxOUMxNyAxNi4zNiAxNC42NyAxMy45OSAxMiAxNFoiIGZpbGw9IiM5OTk5OTkiLz4KPC9zdmc+Cjwvc3ZnPgo8L3N2Zz4K'},
                                    {name: '刘冬梅', title: '中国科学技术发展战略研究院党委书记', avatar: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjAiIGhlaWdodD0iNjAiIHZpZXdCb3g9IjAgMCA2MCA2MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGNpcmNsZSBjeD0iMzAiIGN5PSIzMCIgcj0iMzAiIGZpbGw9IiNGNUY1RjUiLz4KPHN2ZyB4PSIyMCIgeT0iMjAiIHdpZHRoPSIyMCIgaGVpZ2h0PSIyMCIgdmlld0JveD0iMCAwIDI0IDI0IiBmaWxsPSJub25lIj4KPHN2ZyB3aWR0aD0iMjQiIGhlaWdodD0iMjQiIHZpZXdCb3g9IjAgMCAyNCAyNCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTEyIDEyQzE0LjIwOTEgMTIgMTYgMTAuMjA5MSAxNiA4QzE2IDUuNzkwODYgMTQuMjA5MSA0IDEyIDRDOS43OTA4NiA0IDggNS43OTA4NiA4IDhDOCAxMC4yMDkxIDkuNzkwODYgMTIgMTIgMTJaIiBmaWxsPSIjOTk5OTk5Ii8+CjxwYXRoIGQ9Ik0xMiAxNEM5LjMzIDEzLjk5IDcgMTYuMzYgNyAxOVYyMEgxN1YxOUMxNyAxNi4zNiAxNC42NyAxMy45OSAxMiAxNFoiIGZpbGw9IiM5OTk5OTkiLz4KPC9zdmc+Cjwvc3ZnPgo8L3N2Zz4K'}
                                ]
                            }
                        ]
                    }, 1000)
                },

                select(index) {
                    if (this.sel === index) {
                        this.sel = null
                    } else {
                        this.sel = index
                    }
                }
            }
        })
    </script>
</body>
</html>
